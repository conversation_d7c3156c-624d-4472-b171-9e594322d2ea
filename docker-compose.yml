version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: hcagents-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: hcagents@root@123
      MYSQL_DATABASE: hcagents
      MYSQL_USER: hcagents
      MYSQL_PASSWORD: hcagents@root@123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./DATABASE.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - hcagents-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # HCAgents API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hcagents-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=mysql;Database=hcagents;Uid=root;Pwd=hcagents@root@123
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - hcagents-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mysql_data:
    driver: local

networks:
  hcagents-network:
    driver: bridge
