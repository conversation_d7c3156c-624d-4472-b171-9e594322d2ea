# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md

# Build outputs
**/bin/
**/obj/
**/out/

# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# Visual Studio Code
.vscode/

# Rider
.idea/

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# NuGet
*.nupkg
*.snupkg
.nuget/

# Test results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# Coverage reports
*.coverage
*.coveragexml

# Docker
Dockerfile*
.dockerignore

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp
