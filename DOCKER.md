# HCAgents API - Docker Setup

Este documento explica como executar a HCAgents API usando Docker.

## 📋 Pré-requisitos

- Docker Desktop instalado
- Docker Compose (incluído no Docker Desktop)
- PowerShell (para Windows) ou Bash (para Linux/Mac)

## 🚀 Início Rápido

### Opção 1: Docker Compose (Recomendado)

Execute a aplicação completa com MySQL:

```bash
docker-compose up -d
```

Isso irá:
- Criar e executar um container MySQL
- Construir e executar a API HCAgents
- Configurar a rede entre os containers
- Inicializar o banco de dados com o script SQL

### Opção 2: PowerShell Script (Windows)

```powershell
# Executar com docker-compose
.\docker-build.ps1 -Action compose

# Ou apenas construir a imagem
.\docker-build.ps1 -Action build

# Ver logs
.\docker-build.ps1 -Action logs
```

### Opção 3: Comandos Docker Manuais

```bash
# Construir a imagem
docker build -t hcagents-api .

# Executar apenas a API (sem MySQL)
docker run -d --name hcagents-api -p 8080:8080 hcagents-api
```

## 🌐 Acessos

Após executar com sucesso:

- **API**: http://localhost:8080
- **Swagger**: http://localhost:8080/swagger
- **MySQL**: localhost:3306
  - Usuário: `root`
  - Senha: `hcagents@root@123`
  - Database: `hcagents`

## 📁 Estrutura dos Arquivos Docker

```
├── Dockerfile              # Imagem da aplicação .NET
├── docker-compose.yml      # Orquestração completa
├── .dockerignore           # Arquivos ignorados no build
├── docker-build.ps1        # Script de automação (Windows)
└── Api/appsettings.Production.json  # Configuração para produção
```

## 🔧 Configurações

### Variáveis de Ambiente

O docker-compose.yml define as seguintes variáveis:

```yaml
environment:
  - ASPNETCORE_ENVIRONMENT=Production
  - ASPNETCORE_URLS=http://+:8080
  - ConnectionStrings__DefaultConnection=Server=mysql;Database=hcagents;Uid=root;Pwd=hcagents@root@123
```

### Volumes

- `mysql_data`: Persiste os dados do MySQL
- `./DATABASE.sql`: Script de inicialização do banco

## 🛠️ Comandos Úteis

### Gerenciamento de Containers

```bash
# Ver status dos containers
docker-compose ps

# Ver logs em tempo real
docker-compose logs -f

# Parar todos os serviços
docker-compose down

# Parar e remover volumes
docker-compose down -v

# Reconstruir imagens
docker-compose build --no-cache
```

### Debugging

```bash
# Acessar container da API
docker exec -it hcagents-api bash

# Acessar MySQL
docker exec -it hcagents-mysql mysql -u root -p

# Ver logs específicos
docker logs hcagents-api
docker logs hcagents-mysql
```

## 🔍 Health Checks

Ambos os serviços possuem health checks configurados:

- **MySQL**: Verifica conectividade com `mysqladmin ping`
- **API**: Verifica endpoint `/health` (você pode precisar implementar este endpoint)

## 🚨 Troubleshooting

### Problemas Comuns

1. **Porta 3306 já em uso**
   ```bash
   # Alterar porta no docker-compose.yml
   ports:
     - "3307:3306"  # Usar porta 3307 no host
   ```

2. **Porta 8080 já em uso**
   ```bash
   # Alterar porta no docker-compose.yml
   ports:
     - "8081:8080"  # Usar porta 8081 no host
   ```

3. **Erro de conexão com MySQL**
   - Aguarde o MySQL inicializar completamente
   - Verifique os logs: `docker-compose logs mysql`

4. **Rebuild necessário após mudanças**
   ```bash
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```

## 🔒 Segurança

⚠️ **Importante**: Este setup é para desenvolvimento. Para produção:

1. Altere as senhas padrão
2. Use secrets do Docker para credenciais
3. Configure SSL/TLS
4. Implemente autenticação adequada
5. Use imagens base mais seguras

## 📊 Monitoramento

Para monitorar os containers:

```bash
# Uso de recursos
docker stats

# Logs estruturados
docker-compose logs --tail=100 -f api
```

## 🔄 Atualizações

Para atualizar a aplicação:

```bash
# Parar serviços
docker-compose down

# Reconstruir imagem
docker-compose build api

# Reiniciar
docker-compose up -d
```
